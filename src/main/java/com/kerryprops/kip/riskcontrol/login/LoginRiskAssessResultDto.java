package com.kerryprops.kip.riskcontrol.login;

import com.kerryprops.kip.riskcontrol.general.RiskResult;

import java.time.LocalDateTime;
import java.util.List;

public record LoginRiskAssessResultDto(
        String mallCode,
        String unionId,
        RiskResult riskResult,
        List<AssessmentDetail> assessmentDetails,
        LocalDateTime assessmentTime
) {
}
record AssessmentDetail(String riskCode, String description) {
}
