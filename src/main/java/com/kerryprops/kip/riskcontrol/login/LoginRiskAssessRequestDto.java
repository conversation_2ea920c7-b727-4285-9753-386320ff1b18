package com.kerryprops.kip.riskcontrol.login;

import com.kerryprops.kip.riskcontrol.general.TxRiskResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * Login scenario risk assessment request.
 */
@Data
@Schema(description = "Login scenario risk assessment request")
public class LoginRiskAssessRequestDto {

    @NotBlank(message = "Mall code is required")
    @Schema(
            description = "Mall code",
            example = "JAKC",
            allowableValues = {"JAKC", "HKC", "KP", "ALL"}
    )
    private String mallCode;

    @Schema(description = "Phone number", example = "13800138000")
    private String phoneNumber;

    @Schema(description = "Is member frozen", example = "false")
    private Boolean isFrozenMember;

    private Integer smsLoginPhoneNumberCountLast30Days;

    private TxRiskResponse txRiskResponse;
}
