package com.kerryprops.kip.crypto;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * RSA加解密控制器.
 */
@Tag(name = "rsa加解密")
@Slf4j
@Validated
@RestController
@RequestMapping("/crypto/rsa")
@RequiredArgsConstructor
public class RsaCryptoController {

    private final RsaCryptoService rsaService;

    @Operation(summary = "获取rsa公钥")
    @GetMapping("/public_key")
    public PublicKeyResponse getRsaPublicKey() {
        String encodedPublicKey = rsaService.getRsaPublicKey();
        return new PublicKeyResponse(encodedPublicKey);
    }

    /**
     * RSA加密端点.
     *
     * @param request 包含明文数据的请求对象
     * @return 包含加密后数据的响应对象
     */
    @Operation(summary = "rsa加密", description = "RSA加密API")
    @PostMapping(path = "/encrypt",
            consumes = MediaType.APPLICATION_JSON_VALUE)
    public EncryptResponse encryptRsa(@RequestBody EncryptRequest request) {
        String plainText = request.plainText();
        String encrypt = rsaService.encrypt(plainText);
        return new EncryptResponse(encrypt);
    }

    /**
     * RSA解密端点.
     *
     * @param request 包含加密数据的请求对象
     * @return 包含解密后数据的响应对象
     */
    @Hidden
    @Operation(summary = "rsa解密", description = "ras私钥加密")
    @PostMapping(path = "/decrypt",
            consumes = MediaType.APPLICATION_JSON_VALUE)
    public DecryptResponse decryptRsa(@RequestBody DecryptRequest request) {
        String base64Encrypted = request.encryptedData();
        String decrypt = rsaService.decrypt(base64Encrypted);
        return new DecryptResponse(decrypt);
    }


    /**
     * 解密请求数据模型.
     *
     * @param encryptedData 加密数据
     */
    public record DecryptRequest(@NotBlank String encryptedData) {

    }

    public record DecryptResponse(@NotBlank String plainText) {

    }

    /**
     * 加密请求数据模型.
     *
     * @param plainText 明文数据
     */
    public record EncryptRequest(String plainText) {

    }

    public record EncryptResponse(String encryptedData) {

    }

    public record PublicKeyResponse(String base64PublicKey) {

    }

}
