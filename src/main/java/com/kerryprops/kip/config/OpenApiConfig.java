package com.kerryprops.kip.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;

/**
 * OpenAPI/Swagger配置类.
 * 用于自定义Swagger文档的显示和行为.
 *
 * <AUTHOR>
 * @since 4.12.0
 */
@Profile({"dev", "qa", "local"})
@Configuration
public class OpenApiConfig {

    /**
     * 配置OpenAPI基本信息.
     *
     * @return OpenAPI配置实例
     */
    @Bean
    public OpenAPI customOpenAPI(Environment env) {
        String activeProfile = env.getActiveProfiles()[0];
        String serverUrl = switch (activeProfile) {
            case "dev" -> "https://dev-kip-service-internal.kerryonvip.com/toolkit-service";
            case "qa" -> "https://qa-kip-service-internal.kerryplus.com/toolkit-service";
            default -> "http://localhost:8080";
        };
        String serverDescription = switch (activeProfile) {
            case "dev" -> "开发环境";
            case "local" -> "本地开发环境";
            case "qa" -> "Staging环境";
            default -> "默认开发环境";
        };

        return new OpenAPI().info(new Info().title("Kerry Properties Toolkit Service API")
                        .description(
                                "Kerry Properties工具服务API文档，提供风险评估、表单字段配置等功能")
                        .version("4.11.0")
                        .contact(new Contact().name("Kerry Properties Development Team | Ocean")
                                .email("<EMAIL>")
                                .url("https://www.kerryprops.com")))
                .servers(List.of(new Server().url(serverUrl)
                        .description(serverDescription)));
    }

    /**
     * 自定义操作处理器，用于移除不需要的HTTP状态码.
     * 移除401、403、404等默认状态码，只保留明确定义的响应.
     *
     * @return OperationCustomizer实例
     */
    @Bean
    public OperationCustomizer operationCustomizer() {
        return (operation, handlerMethod) -> {
            // 移除默认的401、403、404等状态码
            if (operation.getResponses() != null) {
                operation.getResponses()
                        .remove("401");
                operation.getResponses()
                        .remove("403");
                operation.getResponses()
                        .remove("404");
            }
            return operation;
        };
    }

}