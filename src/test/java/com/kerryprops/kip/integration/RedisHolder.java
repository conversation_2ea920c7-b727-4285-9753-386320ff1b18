package com.kerryprops.kip.integration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.test.util.TestSocketUtils;
import org.springframework.util.function.SingletonSupplier;
import redis.embedded.RedisServer;

import java.io.IOException;
import java.io.UncheckedIOException;

/**
 * RedisHolder.
 *
 * <AUTHOR> 2024-08-07 13:52:02
 **/
@Slf4j
final class RedisHolder {

    private static final SingletonSupplier<RedisServer> REDIS_SERVER_SUPPLIER =
            SingletonSupplier.of(RedisHolder::createRedis);

    private RedisHolder() {
    }

    public static void start() {
        try {
            REDIS_SERVER_SUPPLIER.obtain().start();
        } catch (IOException e) {
            log.error("Failed to start Redis server", e);
        }
    }

    public static int getRedisPort() {
        return REDIS_SERVER_SUPPLIER.obtain().ports().get(0);
    }

    private static RedisServer createRedis() {
        try {
            return new RedisServer(TestSocketUtils.findAvailableTcpPort());
        } catch (IOException e) {
            log.error("Failed to create Redis server", e);
            throw new UncheckedIOException(e);
        }
    }

}
