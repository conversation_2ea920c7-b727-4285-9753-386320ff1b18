package com.kerryprops.kip.integration

import com.kerryprops.kip.riskcontrol.login.LoginRiskAssessResultDto
import com.kerryprops.kip.riskcontrol.login.LoginRiskAssessRequestDto
import org.springframework.stereotype.Service

@Service
class LoginRiskAssessmentService {
    fun assessRisk(loginRiskAssessmentDto: LoginRiskAssessRequestDto): LoginRiskAssessResultDto {
        return LoginRiskAssessResultDto(null,null,null,null,null,)
    }

}
