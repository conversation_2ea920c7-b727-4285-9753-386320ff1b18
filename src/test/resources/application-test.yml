spring:
  datasource:
#    url: **********************************************************************************************************************************************************************************************************************************
#    username: dev_list_query_customizer
#    password: "wy!3DxM0ETa8yY81Vh!nHxNUn6j$EZom"
    url: ***********************************************************************************************************************************************************************************************
    username: root
    password: test
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect

phone:
  virtual-prefixes:
    china-mobile:
      - "165"
      - "1703"
      - "1705"
      - "1706"
    china-unicom:
      - "167"
      - "1704"
      - "1707"
      - "1708"
      - "1709"
      - "171"
    china-telecom:
      - "162"
      - "1700"
      - "1701"
      - "1702"
    china-broadcast:
      - "192"
tencent:
  cloud:
    rce:
      secretId: 'AKIDClPuBF6kANPGwRjpiYHvQipV2MTMzTwC'
      secretKey: '6yd4HH3b4TlBXjlkW9anobDbmpgJjwvn'
      region: 'ap-guangzhou'
distributed:
  jobs:
    enabled: false

drools:
  enabled: true
